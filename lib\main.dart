import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'Pharmalien WebView',
      home: WebViewApp(),
    );
  }
}

class WebViewApp extends StatefulWidget {
  const WebViewApp({super.key});

  @override
  _WebViewAppState createState() => _WebViewAppState();
}

class _WebViewAppState extends State<WebViewApp> with TickerProviderStateMixin {
  late WebViewController _controller;
  String _currentUrl = 'http://vps5.sophatel.com:4201';
  bool _isExternalSite = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  // Define your main domain
  static const String mainDomain = 'http://vps5.sophatel.com:4201';

  @override
  void initState() {
    super.initState();

    // Restore system navigation bar (show default Android navigation)
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom], //
    );

    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onUrlChange: (UrlChange urlChange) {
            _handleUrlChange(urlChange.url ?? '');
          },
          onPageFinished: (String url) {
            // Add any actions to perform when the page finishes loading
            // Inject JavaScript to set localStorage variable
            _controller.runJavaScript('localStorage.setItem("fromFlutterWebView", "true");');
            _handleUrlChange(url);
          },
          onWebResourceError: (WebResourceError error) {
            print('WebView Error: ${error.description}');
            // You can show a custom error page or retry logic here
          },
          onNavigationRequest: (NavigationRequest request) {
            // Allow all navigation requests
            return NavigationDecision.navigate;
          },
        ),
      )
      ..setUserAgent("fromFlutterWebView")
      ..loadRequest(Uri.parse(mainDomain));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleUrlChange(String url) {
    setState(() {
      _currentUrl = url;
      // Check if URL starts with main domain (both HTTP and HTTPS versions)
      bool isMainDomain = url.startsWith(mainDomain) ||
                         url.startsWith(mainDomain.replaceFirst('http://', 'https://'));

      // Show button if: external site OR contains 'assets' (even on main domain)
      bool containsAssets = url.toLowerCase().contains('assets');
      _isExternalSite = !isMainDomain || containsAssets;
    });

    if (_isExternalSite) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }

    // Debug print to see current URL (optional)
    print('Current URL: $_currentUrl, Contains Assets: ${url.toLowerCase().contains('assets')}, Is External: $_isExternalSite');
  }

  void _goBackToMainSite() {
    _controller.loadRequest(Uri.parse(mainDomain));
  }

  void _goBack() async {
    if (await _controller.canGoBack()) {
      await _controller.goBack();
    } else {
      _goBackToMainSite();
    }
  }

  void _goForward() async {
    if (await _controller.canGoForward()) {
      await _controller.goForward();
    }
  }

  void _refresh() {
    _controller.reload();
  }

  void _showMenu() {
    // Placeholder for menu functionality
    print('Menu tapped');
  }

  Widget _buildUltraModernButton({
    required IconData icon,
    required VoidCallback onTap,
    required String tooltip,
    bool isHighlighted = false,
  }) {
    return Tooltip(
      message: tooltip,
      child: GestureDetector(
        onTap: onTap,
        child: TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 150),
          tween: Tween(begin: 1.0, end: 1.0),
          builder: (context, scale, child) {
            return Transform.scale(
              scale: scale,
              child: Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: isHighlighted
                      ? const LinearGradient(
                          colors: [
                            Color(0xFF667eea),
                            Color(0xFF764ba2),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : LinearGradient(
                          colors: [
                            Colors.white.withOpacity(0.1),
                            Colors.white.withOpacity(0.05),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                  border: Border.all(
                    color: isHighlighted
                        ? Colors.white.withOpacity(0.3)
                        : Colors.white.withOpacity(0.1),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: isHighlighted ? Colors.white : Colors.white.withOpacity(0.8),
                  size: isHighlighted ? 28 : 24,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // WebView
            WebViewWidget(controller: _controller),

            // Floating Back Button - ONLY show when on external site
            if (_isExternalSite)
              Positioned(
                top: 16,
                left: 16,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(25),
                          onTap: _goBack,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.white,
                                  size: 20,
                                ),
                                SizedBox(width: 4),
                                Text(
                                  'Retour',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),


          ],
        ),
      ),
      // Ultra Modern Floating Navigation - No Background
      bottomNavigationBar: Container(
        color: Colors.transparent,
        padding: const EdgeInsets.only(bottom: 20, left: 20, right: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildUltraModernButton(
              icon: Icons.arrow_back_ios_new,
              onTap: _goBack,
              tooltip: 'Retour',
            ),
            _buildUltraModernButton(
              icon: Icons.home_filled,
              onTap: _goBackToMainSite,
              tooltip: 'Accueil',
              isHighlighted: true,
            ),
            _buildUltraModernButton(
              icon: Icons.arrow_forward_ios,
              onTap: _goForward,
              tooltip: 'Suivant',
            ),
            _buildUltraModernButton(
              icon: Icons.refresh_rounded,
              onTap: _refresh,
              tooltip: 'Actualiser',
            ),
          ],
        ),
      ),
    );
  }
}
